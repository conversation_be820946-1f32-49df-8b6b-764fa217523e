{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Mission%20me/missionme-website/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Mission%20me/missionme-website/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Mission%20me/missionme-website/src/components/Hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Hero.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Hero.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Mission%20me/missionme-website/src/components/Hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Hero.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Hero.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuQ,GACpS,qCACA", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Mission%20me/missionme-website/src/components/AboutMissionMe.tsx"], "sourcesContent": ["import { Heart, Target, Users, Lightbulb } from 'lucide-react';\n\nconst AboutMissionMe = () => {\n  const features = [\n    {\n      icon: <Heart className=\"w-8 h-8 text-blue-600\" />,\n      title: \"Empathetic Guidance\",\n      description: \"We understand that life's challenges require both wisdom and compassion. Every session is tailored to your unique journey.\"\n    },\n    {\n      icon: <Target className=\"w-8 h-8 text-blue-600\" />,\n      title: \"Clear Direction\",\n      description: \"Cut through the noise and confusion. We help you identify what truly matters and create actionable steps forward.\"\n    },\n    {\n      icon: <Users className=\"w-8 h-8 text-blue-600\" />,\n      title: \"Holistic Approach\",\n      description: \"Whether individual, couples, or family coaching, we address the whole person and all relationships that matter.\"\n    },\n    {\n      icon: <Lightbulb className=\"w-8 h-8 text-blue-600\" />,\n      title: \"Practical Tools\",\n      description: \"Gain real-world strategies and emotional intelligence skills that you can apply immediately in your daily life.\"\n    }\n  ];\n\n  return (\n    <section id=\"about-missionme\" className=\"section-padding bg-gray-50\">\n      <div className=\"container-custom\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            What is MissionMe?\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            MissionMe is more than life coaching—it's a partnership in your personal transformation. \n            We guide individuals, couples, and families through life's most challenging transitions \n            with empathy, clarity, and practical wisdom.\n          </p>\n        </div>\n\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16\">\n          {features.map((feature, index) => (\n            <div\n              key={index}\n              className=\"bg-white p-6 rounded-xl shadow-lg card-hover text-center\"\n            >\n              <div className=\"flex justify-center mb-4\">\n                {feature.icon}\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">\n                {feature.title}\n              </h3>\n              <p className=\"text-gray-600 leading-relaxed\">\n                {feature.description}\n              </p>\n            </div>\n          ))}\n        </div>\n\n        <div className=\"bg-white rounded-2xl shadow-xl p-8 md:p-12\">\n          <div className=\"grid md:grid-cols-2 gap-12 items-center\">\n            <div>\n              <h3 className=\"text-3xl font-bold text-gray-900 mb-6\">\n                How We're Different\n              </h3>\n              <div className=\"space-y-4\">\n                <div className=\"flex items-start gap-3\">\n                  <div className=\"w-2 h-2 bg-blue-600 rounded-full mt-3 flex-shrink-0\"></div>\n                  <p className=\"text-gray-700\">\n                    <strong>Real-world experience:</strong> Nisso brings decades of leadership, \n                    business acumen, and life experience to every conversation.\n                  </p>\n                </div>\n                <div className=\"flex items-start gap-3\">\n                  <div className=\"w-2 h-2 bg-blue-600 rounded-full mt-3 flex-shrink-0\"></div>\n                  <p className=\"text-gray-700\">\n                    <strong>Humor and humanity:</strong> We believe laughter and lightness \n                    are essential ingredients in any transformation journey.\n                  </p>\n                </div>\n                <div className=\"flex items-start gap-3\">\n                  <div className=\"w-2 h-2 bg-blue-600 rounded-full mt-3 flex-shrink-0\"></div>\n                  <p className=\"text-gray-700\">\n                    <strong>Emotional intelligence focus:</strong> We don't just solve problems—we \n                    help you develop the emotional skills to navigate future challenges.\n                  </p>\n                </div>\n                <div className=\"flex items-start gap-3\">\n                  <div className=\"w-2 h-2 bg-blue-600 rounded-full mt-3 flex-shrink-0\"></div>\n                  <p className=\"text-gray-700\">\n                    <strong>Action-oriented:</strong> Every session ends with clear, practical \n                    steps you can take immediately.\n                  </p>\n                </div>\n              </div>\n            </div>\n            <div className=\"bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-8\">\n              <blockquote className=\"text-lg text-gray-700 italic mb-4\">\n                \"Life coaching isn't about having all the answers—it's about asking the right questions \n                and walking alongside you as you discover your own wisdom.\"\n              </blockquote>\n              <cite className=\"text-blue-600 font-semibold\">— Nisso Barokas</cite>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default AboutMissionMe;\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;;;AAEA,MAAM,iBAAiB;IACrB,MAAM,WAAW;QACf;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,4MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAkB,WAAU;kBACtC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAOzE,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;4BAEC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACZ,QAAQ,IAAI;;;;;;8CAEf,8OAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAEhB,8OAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;;2BAVjB;;;;;;;;;;8BAgBX,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAE,WAAU;;0EACX,8OAAC;0EAAO;;;;;;4DAA+B;;;;;;;;;;;;;0DAI3C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAE,WAAU;;0EACX,8OAAC;0EAAO;;;;;;4DAA4B;;;;;;;;;;;;;0DAIxC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAE,WAAU;;0EACX,8OAAC;0EAAO;;;;;;4DAAsC;;;;;;;;;;;;;0DAIlD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAE,WAAU;;0EACX,8OAAC;0EAAO;;;;;;4DAAyB;;;;;;;;;;;;;;;;;;;;;;;;;0CAMzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAW,WAAU;kDAAoC;;;;;;kDAI1D,8OAAC;wCAAK,WAAU;kDAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5D;uCAEe", "debugId": null}}, {"offset": {"line": 445, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Mission%20me/missionme-website/src/components/AboutNisso.tsx"], "sourcesContent": ["import { Shield, Briefcase, Heart, Star } from 'lucide-react';\n\nconst AboutNisso = () => {\n  const credentials = [\n    {\n      icon: <Shield className=\"w-6 h-6 text-blue-600\" />,\n      title: \"Former Special Forces\",\n      description: \"Leadership under pressure, resilience, and strategic thinking\"\n    },\n    {\n      icon: <Briefcase className=\"w-6 h-6 text-blue-600\" />,\n      title: \"Business Leader\",\n      description: \"Decades of executive experience and organizational transformation\"\n    },\n    {\n      icon: <Heart className=\"w-6 h-6 text-blue-600\" />,\n      title: \"Father & Grandfather\",\n      description: \"Deep understanding of family dynamics and generational wisdom\"\n    },\n    {\n      icon: <Star className=\"w-6 h-6 text-blue-600\" />,\n      title: \"Certified Coach\",\n      description: \"Professional training in life coaching and emotional intelligence\"\n    }\n  ];\n\n  return (\n    <section id=\"about-nisso\" className=\"section-padding bg-white\">\n      <div className=\"container-custom\">\n        <div className=\"grid lg:grid-cols-2 gap-16 items-center\">\n          {/* Photo and Quote */}\n          <div className=\"text-center lg:text-left\">\n            <div className=\"relative mb-8\">\n              {/* Photo placeholder */}\n              <div className=\"w-80 h-80 mx-auto lg:mx-0 rounded-2xl bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center shadow-xl\">\n                <span className=\"text-6xl font-bold text-blue-600\">NB</span>\n              </div>\n              {/* Decorative element */}\n              <div className=\"absolute -bottom-4 -right-4 w-24 h-24 bg-blue-600 rounded-full opacity-10\"></div>\n            </div>\n            \n            <blockquote className=\"text-2xl font-medium text-gray-700 italic mb-6\">\n              \"Curiosity is my superpower. Every person I meet teaches me something new about the human experience.\"\n            </blockquote>\n            <cite className=\"text-blue-600 font-semibold text-lg\">— Nisso Barokas</cite>\n          </div>\n\n          {/* Content */}\n          <div>\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n              Meet Nisso Barokas\n            </h2>\n            \n            <div className=\"prose prose-lg text-gray-700 mb-8\">\n              <p className=\"mb-6\">\n                Nisso brings a unique blend of military discipline, business acumen, and deep \n                emotional intelligence to his coaching practice. As a former special forces member \n                and successful business leader, he understands both the strategic and human sides \n                of transformation.\n              </p>\n              \n              <p className=\"mb-6\">\n                But what truly sets Nisso apart is his genuine curiosity about people and his \n                belief that humor and humanity are essential ingredients in any meaningful change. \n                As a father and grandfather, he brings generational wisdom and a deep understanding \n                of family dynamics to his work.\n              </p>\n              \n              <p>\n                Whether you're facing a career transition, relationship challenges, or simply \n                seeking greater clarity and purpose, Nisso meets you where you are with empathy, \n                practical tools, and the occasional well-timed joke.\n              </p>\n            </div>\n\n            {/* Credentials Grid */}\n            <div className=\"grid sm:grid-cols-2 gap-4 mb-8\">\n              {credentials.map((credential, index) => (\n                <div\n                  key={index}\n                  className=\"flex items-start gap-3 p-4 bg-gray-50 rounded-lg\"\n                >\n                  <div className=\"flex-shrink-0 mt-1\">\n                    {credential.icon}\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900 mb-1\">\n                      {credential.title}\n                    </h4>\n                    <p className=\"text-sm text-gray-600\">\n                      {credential.description}\n                    </p>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* Personal Philosophy */}\n            <div className=\"bg-blue-50 rounded-xl p-6\">\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                Nisso's Core Beliefs\n              </h3>\n              <ul className=\"space-y-2 text-gray-700\">\n                <li className=\"flex items-start gap-2\">\n                  <span className=\"text-blue-600 font-bold\">•</span>\n                  Every failure is a gift—you just have to open the box\n                </li>\n                <li className=\"flex items-start gap-2\">\n                  <span className=\"text-blue-600 font-bold\">•</span>\n                  Curiosity is more powerful than judgment\n                </li>\n                <li className=\"flex items-start gap-2\">\n                  <span className=\"text-blue-600 font-bold\">•</span>\n                  Emotional intelligence can be learned and practiced\n                </li>\n                <li className=\"flex items-start gap-2\">\n                  <span className=\"text-blue-600 font-bold\">•</span>\n                  Humor and humanity make everything better\n                </li>\n              </ul>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default AboutNisso;\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;;;AAEA,MAAM,aAAa;IACjB,MAAM,cAAc;QAClB;YACE,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,4MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAc,WAAU;kBAClC,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAmC;;;;;;;;;;;kDAGrD,8OAAC;wCAAI,WAAU;;;;;;;;;;;;0CAGjB,8OAAC;gCAAW,WAAU;0CAAiD;;;;;;0CAGvE,8OAAC;gCAAK,WAAU;0CAAsC;;;;;;;;;;;;kCAIxD,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAIlE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAO;;;;;;kDAOpB,8OAAC;wCAAE,WAAU;kDAAO;;;;;;kDAOpB,8OAAC;kDAAE;;;;;;;;;;;;0CAQL,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,8OAAC;wCAEC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACZ,WAAW,IAAI;;;;;;0DAElB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEACX,WAAW,KAAK;;;;;;kEAEnB,8OAAC;wDAAE,WAAU;kEACV,WAAW,WAAW;;;;;;;;;;;;;uCAXtB;;;;;;;;;;0CAmBX,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAGzD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAK,WAAU;kEAA0B;;;;;;oDAAQ;;;;;;;0DAGpD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAK,WAAU;kEAA0B;;;;;;oDAAQ;;;;;;;0DAGpD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAK,WAAU;kEAA0B;;;;;;oDAAQ;;;;;;;0DAGpD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAK,WAAU;kEAA0B;;;;;;oDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpE;uCAEe", "debugId": null}}, {"offset": {"line": 784, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Mission%20me/missionme-website/src/components/Approach.tsx"], "sourcesContent": ["import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Spark<PERSON> } from 'lucide-react';\n\nconst Approach = () => {\n  const steps = [\n    {\n      number: \"01\",\n      icon: <Eye className=\"w-8 h-8 text-white\" />,\n      title: \"Clarity\",\n      subtitle: \"See What Matters\",\n      description: \"We start by cutting through the noise and confusion to help you see your situation clearly. What are the real issues? What do you truly want? What's holding you back?\",\n      color: \"from-blue-500 to-blue-600\"\n    },\n    {\n      number: \"02\",\n      icon: <Zap className=\"w-8 h-8 text-white\" />,\n      title: \"Confidence\",\n      subtitle: \"Build Inner Strength\",\n      description: \"With clarity comes confidence. We help you recognize your strengths, address limiting beliefs, and develop the emotional intelligence to navigate challenges.\",\n      color: \"from-blue-600 to-blue-700\"\n    },\n    {\n      number: \"03\",\n      icon: <ArrowRight className=\"w-8 h-8 text-white\" />,\n      title: \"Action\",\n      subtitle: \"Take Meaningful Steps\",\n      description: \"Insight without action is just therapy. We create concrete, achievable steps that move you forward, with accountability and support along the way.\",\n      color: \"from-blue-700 to-blue-800\"\n    },\n    {\n      number: \"04\",\n      icon: <Sparkles className=\"w-8 h-8 text-white\" />,\n      title: \"Transformation\",\n      subtitle: \"Become Who You're Meant to Be\",\n      description: \"Real change happens when new patterns become natural. We help you integrate your growth into lasting transformation that serves you for life.\",\n      color: \"from-blue-800 to-blue-900\"\n    }\n  ];\n\n  return (\n    <section id=\"approach\" className=\"section-padding bg-gray-50\">\n      <div className=\"container-custom\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            Nisso's Approach\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            A proven four-part framework that transforms confusion into clarity, \n            doubt into confidence, and dreams into reality.\n          </p>\n        </div>\n\n        {/* Desktop Layout */}\n        <div className=\"hidden lg:block\">\n          <div className=\"relative\">\n            {/* Connection Line */}\n            <div className=\"absolute top-1/2 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 to-blue-900 transform -translate-y-1/2 z-0\"></div>\n            \n            <div className=\"grid grid-cols-4 gap-8 relative z-10\">\n              {steps.map((step, index) => (\n                <div key={index} className=\"text-center\">\n                  {/* Icon Circle */}\n                  <div className={`w-20 h-20 mx-auto mb-6 rounded-full bg-gradient-to-br ${step.color} flex items-center justify-center shadow-lg`}>\n                    {step.icon}\n                  </div>\n                  \n                  {/* Content */}\n                  <div className=\"bg-white rounded-xl p-6 shadow-lg card-hover\">\n                    <div className=\"text-sm font-bold text-blue-600 mb-2\">{step.number}</div>\n                    <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">{step.title}</h3>\n                    <h4 className=\"text-lg font-medium text-blue-600 mb-4\">{step.subtitle}</h4>\n                    <p className=\"text-gray-600 leading-relaxed\">{step.description}</p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Layout */}\n        <div className=\"lg:hidden space-y-8\">\n          {steps.map((step, index) => (\n            <div key={index} className=\"flex gap-6\">\n              {/* Icon and Line */}\n              <div className=\"flex flex-col items-center\">\n                <div className={`w-16 h-16 rounded-full bg-gradient-to-br ${step.color} flex items-center justify-center shadow-lg flex-shrink-0`}>\n                  {step.icon}\n                </div>\n                {index < steps.length - 1 && (\n                  <div className=\"w-1 h-16 bg-gradient-to-b from-blue-500 to-blue-700 mt-4\"></div>\n                )}\n              </div>\n              \n              {/* Content */}\n              <div className=\"bg-white rounded-xl p-6 shadow-lg card-hover flex-1\">\n                <div className=\"text-sm font-bold text-blue-600 mb-2\">{step.number}</div>\n                <h3 className=\"text-xl font-bold text-gray-900 mb-2\">{step.title}</h3>\n                <h4 className=\"text-lg font-medium text-blue-600 mb-4\">{step.subtitle}</h4>\n                <p className=\"text-gray-600 leading-relaxed\">{step.description}</p>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"text-center mt-16\">\n          <div className=\"bg-white rounded-2xl shadow-xl p-8 md:p-12 max-w-4xl mx-auto\">\n            <h3 className=\"text-3xl font-bold text-gray-900 mb-6\">\n              Ready to Start Your Transformation?\n            </h3>\n            <p className=\"text-xl text-gray-600 mb-8 leading-relaxed\">\n              Every journey begins with a single conversation. Let's explore how this framework \n              can work for your unique situation and goals.\n            </p>\n            <a\n              href=\"#contact\"\n              className=\"btn-primary text-lg\"\n            >\n              Schedule Your Discovery Call\n            </a>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Approach;\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;;;AAEA,MAAM,WAAW;IACf,MAAM,QAAQ;QACZ;YACE,QAAQ;YACR,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,OAAO;YACP,UAAU;YACV,aAAa;YACb,OAAO;QACT;QACA;YACE,QAAQ;YACR,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,OAAO;YACP,UAAU;YACV,aAAa;YACb,OAAO;QACT;QACA;YACE,QAAQ;YACR,oBAAM,8OAAC,kNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,OAAO;YACP,UAAU;YACV,aAAa;YACb,OAAO;QACT;QACA;YACE,QAAQ;YACR,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAO;YACP,UAAU;YACV,aAAa;YACb,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAOzE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;wCAAgB,WAAU;;0DAEzB,8OAAC;gDAAI,WAAW,CAAC,sDAAsD,EAAE,KAAK,KAAK,CAAC,2CAA2C,CAAC;0DAC7H,KAAK,IAAI;;;;;;0DAIZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAwC,KAAK,MAAM;;;;;;kEAClE,8OAAC;wDAAG,WAAU;kEAAyC,KAAK,KAAK;;;;;;kEACjE,8OAAC;wDAAG,WAAU;kEAA0C,KAAK,QAAQ;;;;;;kEACrE,8OAAC;wDAAE,WAAU;kEAAiC,KAAK,WAAW;;;;;;;;;;;;;uCAXxD;;;;;;;;;;;;;;;;;;;;;8BAoBlB,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;4BAAgB,WAAU;;8CAEzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,yCAAyC,EAAE,KAAK,KAAK,CAAC,yDAAyD,CAAC;sDAC9H,KAAK,IAAI;;;;;;wCAEX,QAAQ,MAAM,MAAM,GAAG,mBACtB,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAKnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAwC,KAAK,MAAM;;;;;;sDAClE,8OAAC;4CAAG,WAAU;sDAAwC,KAAK,KAAK;;;;;;sDAChE,8OAAC;4CAAG,WAAU;sDAA0C,KAAK,QAAQ;;;;;;sDACrE,8OAAC;4CAAE,WAAU;sDAAiC,KAAK,WAAW;;;;;;;;;;;;;2BAhBxD;;;;;;;;;;8BAuBd,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAA6C;;;;;;0CAI1D,8OAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}, {"offset": {"line": 1113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Mission%20me/missionme-website/src/components/Services.tsx"], "sourcesContent": ["import { User, Users, Heart, Compass } from 'lucide-react';\n\nconst Services = () => {\n  const services = [\n    {\n      icon: <User className=\"w-12 h-12 text-blue-600\" />,\n      title: \"Individual Coaching\",\n      subtitle: \"Personal Growth & Life Transitions\",\n      description: \"Navigate career changes, personal challenges, and major life decisions with clarity and confidence.\",\n      features: [\n        \"Career transition guidance\",\n        \"Personal development planning\",\n        \"Decision-making support\",\n        \"Confidence building\",\n        \"Goal setting and achievement\"\n      ],\n      ideal: \"Perfect for professionals, entrepreneurs, and anyone facing major life changes\"\n    },\n    {\n      icon: <Heart className=\"w-12 h-12 text-blue-600\" />,\n      title: \"Couples & Family Coaching\",\n      subtitle: \"Relationship Harmony & Communication\",\n      description: \"Strengthen relationships, resolve conflicts, and build deeper emotional connections with those who matter most.\",\n      features: [\n        \"Communication skills training\",\n        \"Conflict resolution strategies\",\n        \"Emotional intelligence development\",\n        \"Family dynamics improvement\",\n        \"Relationship goal setting\"\n      ],\n      ideal: \"Ideal for couples and families seeking stronger, healthier relationships\"\n    },\n    {\n      icon: <Compass className=\"w-12 h-12 text-blue-600\" />,\n      title: \"Young Adult Coaching\",\n      subtitle: \"Direction & Emotional Strength\",\n      description: \"Help young adults find their path, build emotional resilience, and develop the skills for lifelong success.\",\n      features: [\n        \"Life direction and purpose\",\n        \"Emotional intelligence training\",\n        \"Career exploration\",\n        \"Relationship skills\",\n        \"Stress management techniques\"\n      ],\n      ideal: \"Designed for young adults (20s-30s) seeking clarity and emotional growth\"\n    },\n    {\n      icon: <Users className=\"w-12 h-12 text-blue-600\" />,\n      title: \"Group Workshops\",\n      subtitle: \"Collective Growth & Learning\",\n      description: \"Join like-minded individuals in focused workshops on emotional intelligence, leadership, and personal development.\",\n      features: [\n        \"Emotional intelligence workshops\",\n        \"Leadership development\",\n        \"Communication masterclasses\",\n        \"Goal-setting intensives\",\n        \"Peer learning and support\"\n      ],\n      ideal: \"Great for teams, organizations, and individuals who learn well in groups\"\n    }\n  ];\n\n  return (\n    <section id=\"services\" className=\"section-padding bg-white\">\n      <div className=\"container-custom\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            Coaching Services\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            Whether you're navigating life solo, strengthening relationships, or building \n            emotional intelligence, we have a coaching approach tailored to your needs.\n          </p>\n        </div>\n\n        <div className=\"grid lg:grid-cols-2 gap-8 mb-16\">\n          {services.map((service, index) => (\n            <div\n              key={index}\n              className=\"bg-gray-50 rounded-2xl p-8 card-hover\"\n            >\n              {/* Header */}\n              <div className=\"flex items-start gap-4 mb-6\">\n                <div className=\"flex-shrink-0\">\n                  {service.icon}\n                </div>\n                <div>\n                  <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">\n                    {service.title}\n                  </h3>\n                  <p className=\"text-lg font-medium text-blue-600\">\n                    {service.subtitle}\n                  </p>\n                </div>\n              </div>\n\n              {/* Description */}\n              <p className=\"text-gray-700 mb-6 leading-relaxed\">\n                {service.description}\n              </p>\n\n              {/* Features */}\n              <div className=\"mb-6\">\n                <h4 className=\"font-semibold text-gray-900 mb-3\">What's Included:</h4>\n                <ul className=\"space-y-2\">\n                  {service.features.map((feature, featureIndex) => (\n                    <li key={featureIndex} className=\"flex items-start gap-2 text-gray-700\">\n                      <span className=\"text-blue-600 font-bold mt-1\">•</span>\n                      {feature}\n                    </li>\n                  ))}\n                </ul>\n              </div>\n\n              {/* Ideal For */}\n              <div className=\"bg-blue-50 rounded-lg p-4\">\n                <p className=\"text-sm font-medium text-blue-800\">\n                  <span className=\"font-semibold\">Ideal for:</span> {service.ideal}\n                </p>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Pricing and Process */}\n        <div className=\"bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-8 md:p-12\">\n          <div className=\"grid md:grid-cols-2 gap-12\">\n            <div>\n              <h3 className=\"text-3xl font-bold text-gray-900 mb-6\">\n                How It Works\n              </h3>\n              <div className=\"space-y-4\">\n                <div className=\"flex gap-4\">\n                  <div className=\"w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0\">\n                    1\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900\">Discovery Call</h4>\n                    <p className=\"text-gray-700\">Free 30-minute conversation to understand your goals and see if we're a good fit.</p>\n                  </div>\n                </div>\n                <div className=\"flex gap-4\">\n                  <div className=\"w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0\">\n                    2\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900\">Assessment & Planning</h4>\n                    <p className=\"text-gray-700\">Deep dive into your current situation and create a personalized coaching plan.</p>\n                  </div>\n                </div>\n                <div className=\"flex gap-4\">\n                  <div className=\"w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0\">\n                    3\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900\">Regular Sessions</h4>\n                    <p className=\"text-gray-700\">Weekly or bi-weekly sessions with homework, tools, and ongoing support.</p>\n                  </div>\n                </div>\n                <div className=\"flex gap-4\">\n                  <div className=\"w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0\">\n                    4\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900\">Integration & Growth</h4>\n                    <p className=\"text-gray-700\">Apply new skills, track progress, and celebrate your transformation.</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n            \n            <div>\n              <h3 className=\"text-3xl font-bold text-gray-900 mb-6\">\n                Investment in Yourself\n              </h3>\n              <div className=\"bg-white rounded-xl p-6 shadow-lg\">\n                <p className=\"text-gray-700 mb-4\">\n                  Coaching packages are customized based on your specific needs and goals. \n                  During your discovery call, we'll discuss the best approach and investment \n                  for your situation.\n                </p>\n                <p className=\"text-gray-700 mb-6\">\n                  <strong>All packages include:</strong> Regular sessions, email support, \n                  practical tools and exercises, and a commitment to your success.\n                </p>\n                <a\n                  href=\"#contact\"\n                  className=\"btn-primary w-full text-center\"\n                >\n                  Schedule Discovery Call\n                </a>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Services;\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;;;AAEA,MAAM,WAAW;IACf,MAAM,WAAW;QACf;YACE,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,OAAO;YACP,UAAU;YACV,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,OAAO;QACT;QACA;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,UAAU;YACV,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,OAAO;QACT;QACA;YACE,oBAAM,8OAAC,wMAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,OAAO;YACP,UAAU;YACV,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,OAAO;QACT;QACA;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,UAAU;YACV,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAMzE,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;4BAEC,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,QAAQ,IAAI;;;;;;sDAEf,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DACX,QAAQ,KAAK;;;;;;8DAEhB,8OAAC;oDAAE,WAAU;8DACV,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;8CAMvB,8OAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;8CAItB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAG,WAAU;sDACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,8OAAC;oDAAsB,WAAU;;sEAC/B,8OAAC;4DAAK,WAAU;sEAA+B;;;;;;wDAC9C;;mDAFM;;;;;;;;;;;;;;;;8CASf,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;0DACX,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;4CAAiB;4CAAE,QAAQ,KAAK;;;;;;;;;;;;;2BAvC/D;;;;;;;;;;8BA+CX,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAA+G;;;;;;kEAG9H,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA8B;;;;;;0EAC5C,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;0DAGjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAA+G;;;;;;kEAG9H,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA8B;;;;;;0EAC5C,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;0DAGjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAA+G;;;;;;kEAG9H,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA8B;;;;;;0EAC5C,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;0DAGjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAA+G;;;;;;kEAG9H,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA8B;;;;;;0EAC5C,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMrC,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAKlC,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;kEAAO;;;;;;oDAA8B;;;;;;;0DAGxC,8OAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;uCAEe", "debugId": null}}, {"offset": {"line": 1653, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Mission%20me/missionme-website/src/components/Testimonials.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Testimonials.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Testimonials.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmS,GAChU,iEACA", "debugId": null}}, {"offset": {"line": 1667, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Mission%20me/missionme-website/src/components/Testimonials.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Testimonials.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Testimonials.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+Q,GAC5S,6CACA", "debugId": null}}, {"offset": {"line": 1681, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1691, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Mission%20me/missionme-website/src/components/FAQ.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/FAQ.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/FAQ.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0R,GACvT,wDACA", "debugId": null}}, {"offset": {"line": 1705, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Mission%20me/missionme-website/src/components/FAQ.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/FAQ.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/FAQ.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsQ,GACnS,oCACA", "debugId": null}}, {"offset": {"line": 1719, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1729, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Mission%20me/missionme-website/src/components/PersonalQuotes.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/PersonalQuotes.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/PersonalQuotes.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqS,GAClU,mEACA", "debugId": null}}, {"offset": {"line": 1743, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Mission%20me/missionme-website/src/components/PersonalQuotes.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/PersonalQuotes.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/PersonalQuotes.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiR,GAC9S,+CACA", "debugId": null}}, {"offset": {"line": 1757, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1767, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Mission%20me/missionme-website/src/components/Blog.tsx"], "sourcesContent": ["import { Calendar, ArrowR<PERSON>, Clock } from 'lucide-react';\n\nconst Blog = () => {\n  const blogPosts = [\n    {\n      title: \"The Art of Asking Better Questions\",\n      excerpt: \"How curiosity can transform your relationships, career, and personal growth. Learn the questions that unlock deeper understanding and better outcomes.\",\n      date: \"March 15, 2024\",\n      readTime: \"5 min read\",\n      category: \"Personal Development\",\n      image: \"/api/placeholder/400/250\",\n      slug: \"art-of-asking-better-questions\"\n    },\n    {\n      title: \"Emotional Intelligence in the Workplace\",\n      excerpt: \"Why EQ matters more than IQ in today's professional world. Practical strategies for building emotional intelligence that advances your career.\",\n      date: \"March 8, 2024\",\n      readTime: \"7 min read\",\n      category: \"Leadership\",\n      image: \"/api/placeholder/400/250\",\n      slug: \"emotional-intelligence-workplace\"\n    },\n    {\n      title: \"Navigating Life Transitions with Grace\",\n      excerpt: \"Whether it's a career change, relationship shift, or major life decision, learn how to move through transitions with confidence and clarity.\",\n      date: \"March 1, 2024\",\n      readTime: \"6 min read\",\n      category: \"Life Transitions\",\n      image: \"/api/placeholder/400/250\",\n      slug: \"navigating-life-transitions\"\n    }\n  ];\n\n  const categories = [\"All\", \"Personal Development\", \"Leadership\", \"Life Transitions\", \"Relationships\"];\n\n  return (\n    <section id=\"blog\" className=\"section-padding bg-gray-50\">\n      <div className=\"container-custom\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            Insights & Reflections\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            Monthly insights on personal development, emotional intelligence, and life transformation. \n            Real wisdom for real challenges.\n          </p>\n        </div>\n\n        {/* Category Filter */}\n        <div className=\"flex flex-wrap justify-center gap-3 mb-12\">\n          {categories.map((category, index) => (\n            <button\n              key={index}\n              className={`px-4 py-2 rounded-full font-medium transition-all duration-300 ${\n                index === 0\n                  ? 'bg-blue-600 text-white shadow-lg'\n                  : 'bg-white text-gray-600 hover:bg-blue-50 hover:text-blue-600 shadow-sm'\n              }`}\n            >\n              {category}\n            </button>\n          ))}\n        </div>\n\n        {/* Featured Post */}\n        <div className=\"bg-white rounded-2xl shadow-xl overflow-hidden mb-12\">\n          <div className=\"grid lg:grid-cols-2 gap-0\">\n            <div className=\"bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center p-12\">\n              <div className=\"text-center\">\n                <div className=\"w-32 h-32 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <span className=\"text-white text-4xl font-bold\">📝</span>\n                </div>\n                <p className=\"text-blue-800 font-medium\">Featured Article</p>\n              </div>\n            </div>\n            <div className=\"p-8 lg:p-12 flex flex-col justify-center\">\n              <div className=\"flex items-center gap-4 mb-4\">\n                <span className=\"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium\">\n                  {blogPosts[0].category}\n                </span>\n                <div className=\"flex items-center text-gray-500 text-sm\">\n                  <Calendar className=\"w-4 h-4 mr-1\" />\n                  {blogPosts[0].date}\n                </div>\n                <div className=\"flex items-center text-gray-500 text-sm\">\n                  <Clock className=\"w-4 h-4 mr-1\" />\n                  {blogPosts[0].readTime}\n                </div>\n              </div>\n              <h3 className=\"text-2xl md:text-3xl font-bold text-gray-900 mb-4\">\n                {blogPosts[0].title}\n              </h3>\n              <p className=\"text-gray-600 mb-6 leading-relaxed\">\n                {blogPosts[0].excerpt}\n              </p>\n              <a\n                href={`/blog/${blogPosts[0].slug}`}\n                className=\"inline-flex items-center text-blue-600 font-semibold hover:text-blue-700 transition-colors duration-200\"\n              >\n                Read Full Article\n                <ArrowRight className=\"w-4 h-4 ml-2\" />\n              </a>\n            </div>\n          </div>\n        </div>\n\n        {/* Recent Posts Grid */}\n        <div className=\"grid md:grid-cols-2 gap-8 mb-12\">\n          {blogPosts.slice(1).map((post, index) => (\n            <article\n              key={index}\n              className=\"bg-white rounded-xl shadow-lg overflow-hidden card-hover\"\n            >\n              <div className=\"h-48 bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center\">\n                <span className=\"text-4xl\">📖</span>\n              </div>\n              <div className=\"p-6\">\n                <div className=\"flex items-center gap-3 mb-3\">\n                  <span className=\"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium\">\n                    {post.category}\n                  </span>\n                  <div className=\"flex items-center text-gray-500 text-sm\">\n                    <Calendar className=\"w-4 h-4 mr-1\" />\n                    {post.date}\n                  </div>\n                </div>\n                <h3 className=\"text-xl font-bold text-gray-900 mb-3\">\n                  {post.title}\n                </h3>\n                <p className=\"text-gray-600 mb-4 leading-relaxed\">\n                  {post.excerpt}\n                </p>\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center text-gray-500 text-sm\">\n                    <Clock className=\"w-4 h-4 mr-1\" />\n                    {post.readTime}\n                  </div>\n                  <a\n                    href={`/blog/${post.slug}`}\n                    className=\"inline-flex items-center text-blue-600 font-semibold hover:text-blue-700 transition-colors duration-200\"\n                  >\n                    Read More\n                    <ArrowRight className=\"w-4 h-4 ml-1\" />\n                  </a>\n                </div>\n              </div>\n            </article>\n          ))}\n        </div>\n\n        {/* Newsletter Signup */}\n        <div className=\"bg-white rounded-2xl shadow-xl p-8 md:p-12 text-center\">\n          <h3 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Stay Connected\n          </h3>\n          <p className=\"text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed\">\n            Get monthly insights, practical tools, and inspiration delivered to your inbox. \n            No spam, just valuable content to support your growth journey.\n          </p>\n          <div className=\"max-w-md mx-auto flex gap-3\">\n            <input\n              type=\"email\"\n              placeholder=\"Enter your email\"\n              className=\"flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent\"\n            />\n            <button className=\"btn-primary whitespace-nowrap\">\n              Subscribe\n            </button>\n          </div>\n          <p className=\"text-sm text-gray-500 mt-4\">\n            Unsubscribe anytime. We respect your privacy.\n          </p>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Blog;\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;;;AAEA,MAAM,OAAO;IACX,MAAM,YAAY;QAChB;YACE,OAAO;YACP,SAAS;YACT,MAAM;YACN,UAAU;YACV,UAAU;YACV,OAAO;YACP,MAAM;QACR;QACA;YACE,OAAO;YACP,SAAS;YACT,MAAM;YACN,UAAU;YACV,UAAU;YACV,OAAO;YACP,MAAM;QACR;QACA;YACE,OAAO;YACP,SAAS;YACT,MAAM;YACN,UAAU;YACV,UAAU;YACV,OAAO;YACP,MAAM;QACR;KACD;IAED,MAAM,aAAa;QAAC;QAAO;QAAwB;QAAc;QAAoB;KAAgB;IAErG,qBACE,8OAAC;QAAQ,IAAG;QAAO,WAAU;kBAC3B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAOzE,8OAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,8OAAC;4BAEC,WAAW,CAAC,+DAA+D,EACzE,UAAU,IACN,qCACA,yEACJ;sCAED;2BAPI;;;;;;;;;;8BAaX,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;sDAElD,8OAAC;4CAAE,WAAU;sDAA4B;;;;;;;;;;;;;;;;;0CAG7C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DACb,SAAS,CAAC,EAAE,CAAC,QAAQ;;;;;;0DAExB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDACnB,SAAS,CAAC,EAAE,CAAC,IAAI;;;;;;;0DAEpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAChB,SAAS,CAAC,EAAE,CAAC,QAAQ;;;;;;;;;;;;;kDAG1B,8OAAC;wCAAG,WAAU;kDACX,SAAS,CAAC,EAAE,CAAC,KAAK;;;;;;kDAErB,8OAAC;wCAAE,WAAU;kDACV,SAAS,CAAC,EAAE,CAAC,OAAO;;;;;;kDAEvB,8OAAC;wCACC,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,CAAC,IAAI,EAAE;wCAClC,WAAU;;4CACX;0DAEC,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO9B,8OAAC;oBAAI,WAAU;8BACZ,UAAU,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,MAAM,sBAC7B,8OAAC;4BAEC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAAW;;;;;;;;;;;8CAE7B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DACb,KAAK,QAAQ;;;;;;8DAEhB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDACnB,KAAK,IAAI;;;;;;;;;;;;;sDAGd,8OAAC;4CAAG,WAAU;sDACX,KAAK,KAAK;;;;;;sDAEb,8OAAC;4CAAE,WAAU;sDACV,KAAK,OAAO;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAChB,KAAK,QAAQ;;;;;;;8DAEhB,8OAAC;oDACC,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;oDAC1B,WAAU;;wDACX;sEAEC,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;2BAhCvB;;;;;;;;;;8BAyCX,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAA+D;;;;;;sCAI5E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,WAAU;;;;;;8CAEZ,8OAAC;oCAAO,WAAU;8CAAgC;;;;;;;;;;;;sCAIpD,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;;;;;;;AAOpD;uCAEe", "debugId": null}}, {"offset": {"line": 2220, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Mission%20me/missionme-website/src/components/Contact.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Contact.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Contact.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8R,GAC3T,4DACA", "debugId": null}}, {"offset": {"line": 2234, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Mission%20me/missionme-website/src/components/Contact.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Contact.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Contact.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0Q,GACvS,wCACA", "debugId": null}}, {"offset": {"line": 2248, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2258, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Mission%20me/missionme-website/src/components/Footer.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { Mail, Phone, MapPin, Linkedin, Twitter, Facebook } from 'lucide-react';\n\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n\n  const navigation = {\n    main: [\n      { name: 'About MissionMe', href: '#about-missionme' },\n      { name: 'About Nisso', href: '#about-nisso' },\n      { name: 'Approach', href: '#approach' },\n      { name: 'Services', href: '#services' },\n      { name: 'Testimonials', href: '#testimonials' },\n      { name: 'Blog', href: '#blog' },\n      { name: 'Contact', href: '#contact' },\n    ],\n    services: [\n      { name: 'Individual Coaching', href: '#services' },\n      { name: 'Couples Coaching', href: '#services' },\n      { name: 'Family Coaching', href: '#services' },\n      { name: 'Young Adult Coaching', href: '#services' },\n      { name: 'Group Workshops', href: '#services' },\n    ],\n    resources: [\n      { name: 'FAQ', href: '#faq' },\n      { name: 'Blog', href: '#blog' },\n      { name: 'Personal Quotes', href: '#quotes' },\n      { name: 'Discovery Call', href: '#contact' },\n    ],\n    legal: [\n      { name: 'Privacy Policy', href: '/privacy' },\n      { name: 'Terms of Service', href: '/terms' },\n      { name: 'Coaching Agreement', href: '/coaching-agreement' },\n    ]\n  };\n\n  const socialLinks = [\n    { name: 'LinkedIn', href: '#', icon: Linkedin },\n    { name: 'Twitter', href: '#', icon: Twitter },\n    { name: 'Facebook', href: '#', icon: Facebook },\n  ];\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"container-custom\">\n        {/* Main Footer Content */}\n        <div className=\"py-16\">\n          <div className=\"grid lg:grid-cols-4 gap-8\">\n            {/* Brand Section */}\n            <div className=\"lg:col-span-1\">\n              <Link href=\"#home\" className=\"flex items-center space-x-2 mb-6\">\n                <div className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-700 rounded-full flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-lg\">M</span>\n                </div>\n                <span className=\"text-xl font-bold\">MissionMe</span>\n              </Link>\n              <p className=\"text-gray-300 mb-6 leading-relaxed\">\n                With You, Every Stroke of the Way. Life coaching by Nisso Barokas, \n                helping individuals, couples, and families navigate life's challenges \n                with empathy, clarity, and practical wisdom.\n              </p>\n              \n              {/* Contact Info */}\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center gap-3\">\n                  <Mail className=\"w-5 h-5 text-blue-400\" />\n                  <a href=\"mailto:<EMAIL>\" className=\"text-gray-300 hover:text-white transition-colors\">\n                    <EMAIL>\n                  </a>\n                </div>\n                <div className=\"flex items-center gap-3\">\n                  <Phone className=\"w-5 h-5 text-blue-400\" />\n                  <a href=\"tel:+15551234567\" className=\"text-gray-300 hover:text-white transition-colors\">\n                    +****************\n                  </a>\n                </div>\n                <div className=\"flex items-center gap-3\">\n                  <MapPin className=\"w-5 h-5 text-blue-400\" />\n                  <span className=\"text-gray-300\">Available Online & In-Person</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Navigation Links */}\n            <div className=\"lg:col-span-3\">\n              <div className=\"grid md:grid-cols-3 gap-8\">\n                {/* Main Navigation */}\n                <div>\n                  <h3 className=\"text-lg font-semibold mb-4\">Navigation</h3>\n                  <ul className=\"space-y-2\">\n                    {navigation.main.map((item) => (\n                      <li key={item.name}>\n                        <Link\n                          href={item.href}\n                          className=\"text-gray-300 hover:text-white transition-colors duration-200\"\n                        >\n                          {item.name}\n                        </Link>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n\n                {/* Services */}\n                <div>\n                  <h3 className=\"text-lg font-semibold mb-4\">Services</h3>\n                  <ul className=\"space-y-2\">\n                    {navigation.services.map((item) => (\n                      <li key={item.name}>\n                        <Link\n                          href={item.href}\n                          className=\"text-gray-300 hover:text-white transition-colors duration-200\"\n                        >\n                          {item.name}\n                        </Link>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n\n                {/* Resources */}\n                <div>\n                  <h3 className=\"text-lg font-semibold mb-4\">Resources</h3>\n                  <ul className=\"space-y-2\">\n                    {navigation.resources.map((item) => (\n                      <li key={item.name}>\n                        <Link\n                          href={item.href}\n                          className=\"text-gray-300 hover:text-white transition-colors duration-200\"\n                        >\n                          {item.name}\n                        </Link>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Newsletter Signup */}\n        <div className=\"border-t border-gray-800 py-8\">\n          <div className=\"grid md:grid-cols-2 gap-8 items-center\">\n            <div>\n              <h3 className=\"text-xl font-semibold mb-2\">Stay Connected</h3>\n              <p className=\"text-gray-300\">\n                Get monthly insights and practical tools for your growth journey.\n              </p>\n            </div>\n            <div className=\"flex gap-3\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"flex-1 px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent text-white placeholder-gray-400\"\n              />\n              <button className=\"bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-300\">\n                Subscribe\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Footer */}\n        <div className=\"border-t border-gray-800 py-8\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center gap-4\">\n            {/* Copyright */}\n            <div className=\"text-gray-400 text-sm\">\n              © {currentYear} MissionMe. All rights reserved.\n            </div>\n\n            {/* Social Links */}\n            <div className=\"flex items-center gap-4\">\n              {socialLinks.map((social) => {\n                const Icon = social.icon;\n                return (\n                  <a\n                    key={social.name}\n                    href={social.href}\n                    className=\"w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center text-gray-400 hover:text-white hover:bg-blue-600 transition-all duration-300\"\n                    aria-label={social.name}\n                  >\n                    <Icon className=\"w-5 h-5\" />\n                  </a>\n                );\n              })}\n            </div>\n\n            {/* Legal Links */}\n            <div className=\"flex items-center gap-4 text-sm\">\n              {navigation.legal.map((item, index) => (\n                <span key={item.name} className=\"flex items-center gap-4\">\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                  >\n                    {item.name}\n                  </Link>\n                  {index < navigation.legal.length - 1 && (\n                    <span className=\"text-gray-600\">|</span>\n                  )}\n                </span>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AAEA,MAAM,SAAS;IACb,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,aAAa;QACjB,MAAM;YACJ;gBAAE,MAAM;gBAAmB,MAAM;YAAmB;YACpD;gBAAE,MAAM;gBAAe,MAAM;YAAe;YAC5C;gBAAE,MAAM;gBAAY,MAAM;YAAY;YACtC;gBAAE,MAAM;gBAAY,MAAM;YAAY;YACtC;gBAAE,MAAM;gBAAgB,MAAM;YAAgB;YAC9C;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAW,MAAM;YAAW;SACrC;QACD,UAAU;YACR;gBAAE,MAAM;gBAAuB,MAAM;YAAY;YACjD;gBAAE,MAAM;gBAAoB,MAAM;YAAY;YAC9C;gBAAE,MAAM;gBAAmB,MAAM;YAAY;YAC7C;gBAAE,MAAM;gBAAwB,MAAM;YAAY;YAClD;gBAAE,MAAM;gBAAmB,MAAM;YAAY;SAC9C;QACD,WAAW;YACT;gBAAE,MAAM;gBAAO,MAAM;YAAO;YAC5B;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAmB,MAAM;YAAU;YAC3C;gBAAE,MAAM;gBAAkB,MAAM;YAAW;SAC5C;QACD,OAAO;YACL;gBAAE,MAAM;gBAAkB,MAAM;YAAW;YAC3C;gBAAE,MAAM;gBAAoB,MAAM;YAAS;YAC3C;gBAAE,MAAM;gBAAsB,MAAM;YAAsB;SAC3D;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM;YAAY,MAAM;YAAK,MAAM,0MAAA,CAAA,WAAQ;QAAC;QAC9C;YAAE,MAAM;YAAW,MAAM;YAAK,MAAM,wMAAA,CAAA,UAAO;QAAC;QAC5C;YAAE,MAAM;YAAY,MAAM;YAAK,MAAM,0MAAA,CAAA,WAAQ;QAAC;KAC/C;IAED,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAQ,WAAU;;0DAC3B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,8OAAC;gDAAK,WAAU;0DAAoB;;;;;;;;;;;;kDAEtC,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAOlD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDAAE,MAAK;wDAA6B,WAAU;kEAAmD;;;;;;;;;;;;0DAIpG,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAE,MAAK;wDAAmB,WAAU;kEAAmD;;;;;;;;;;;;0DAI1F,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;;;;;;;0CAMtC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,8OAAC;oDAAG,WAAU;8DACX,WAAW,IAAI,CAAC,GAAG,CAAC,CAAC,qBACpB,8OAAC;sEACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAM,KAAK,IAAI;gEACf,WAAU;0EAET,KAAK,IAAI;;;;;;2DALL,KAAK,IAAI;;;;;;;;;;;;;;;;sDAaxB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,8OAAC;oDAAG,WAAU;8DACX,WAAW,QAAQ,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sEACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAM,KAAK,IAAI;gEACf,WAAU;0EAET,KAAK,IAAI;;;;;;2DALL,KAAK,IAAI;;;;;;;;;;;;;;;;sDAaxB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,8OAAC;oDAAG,WAAU;8DACX,WAAW,SAAS,CAAC,GAAG,CAAC,CAAC,qBACzB,8OAAC;sEACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAM,KAAK,IAAI;gEACf,WAAU;0EAET,KAAK,IAAI;;;;;;2DALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAiBhC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAI/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC;wCAAO,WAAU;kDAA6G;;;;;;;;;;;;;;;;;;;;;;;8BAQrI,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;oCAAwB;oCAClC;oCAAY;;;;;;;0CAIjB,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC;oCAChB,MAAM,OAAO,OAAO,IAAI;oCACxB,qBACE,8OAAC;wCAEC,MAAM,OAAO,IAAI;wCACjB,WAAU;wCACV,cAAY,OAAO,IAAI;kDAEvB,cAAA,8OAAC;4CAAK,WAAU;;;;;;uCALX,OAAO,IAAI;;;;;gCAQtB;;;;;;0CAIF,8OAAC;gCAAI,WAAU;0CACZ,WAAW,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC3B,8OAAC;wCAAqB,WAAU;;0DAC9B,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;4CAEX,QAAQ,WAAW,KAAK,CAAC,MAAM,GAAG,mBACjC,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;uCARzB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBpC;uCAEe", "debugId": null}}, {"offset": {"line": 2833, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Mission%20me/missionme-website/src/app/page.tsx"], "sourcesContent": ["import Header from \"@/components/Header\";\nimport Hero from \"@/components/Hero\";\nimport AboutMissionMe from \"@/components/AboutMissionMe\";\nimport AboutNisso from \"@/components/AboutNisso\";\nimport Approach from \"@/components/Approach\";\nimport Services from \"@/components/Services\";\nimport Testimonials from \"@/components/Testimonials\";\nimport FAQ from \"@/components/FAQ\";\nimport PersonalQuotes from \"@/components/PersonalQuotes\";\nimport Blog from \"@/components/Blog\";\nimport Contact from \"@/components/Contact\";\nimport Footer from \"@/components/Footer\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen\">\n      <Header />\n      <main>\n        <Hero />\n        <AboutMissionMe />\n        <AboutNisso />\n        <Approach />\n        <Services />\n        <Testimonials />\n        <FAQ />\n        <PersonalQuotes />\n        <Blog />\n        <Contact />\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BACP,8OAAC;;kCACC,8OAAC,0HAAA,CAAA,UAAI;;;;;kCACL,8OAAC,oIAAA,CAAA,UAAc;;;;;kCACf,8OAAC,gIAAA,CAAA,UAAU;;;;;kCACX,8OAAC,8HAAA,CAAA,UAAQ;;;;;kCACT,8OAAC,8HAAA,CAAA,UAAQ;;;;;kCACT,8OAAC,kIAAA,CAAA,UAAY;;;;;kCACb,8OAAC,yHAAA,CAAA,UAAG;;;;;kCACJ,8OAAC,oIAAA,CAAA,UAAc;;;;;kCACf,8OAAC,0HAAA,CAAA,UAAI;;;;;kCACL,8OAAC,6HAAA,CAAA,UAAO;;;;;;;;;;;0BAEV,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}]}