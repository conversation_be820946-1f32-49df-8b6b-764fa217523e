"use client";

import { useState, useEffect } from "react";
import { ChevronDown, Play } from "lucide-react";
import Link from "next/link";

const Hero = () => {
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);

  useEffect(() => {
    // Simulate video loading
    const timer = setTimeout(() => setIsVideoLoaded(true), 1000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <section
      id="home"
      className="relative min-h-screen flex items-center justify-center overflow-hidden pt-20"
    >
      {/* Background Video Placeholder */}
      <div className="absolute inset-0 z-0">
        {isVideoLoaded ? (
          <div className="w-full h-full bg-gradient-to-br from-blue-400 via-blue-600 to-blue-800 opacity-90">
            {/* Animated water effect */}
            <div className="absolute inset-0 bg-wave opacity-70"></div>
            <div className="absolute inset-0">
              <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-blue-300 rounded-full opacity-20 animate-pulse"></div>
              <div className="absolute top-3/4 right-1/4 w-24 h-24 bg-blue-200 rounded-full opacity-30 animate-pulse delay-1000"></div>
              <div className="absolute bottom-1/4 left-1/3 w-20 h-20 bg-blue-400 rounded-full opacity-25 animate-pulse delay-500"></div>
            </div>
          </div>
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-blue-400 to-blue-800 flex items-center justify-center">
            <div className="text-white text-lg">Loading...</div>
          </div>
        )}
      </div>

      {/* Content Overlay */}
      <div className="relative z-10 text-center text-white container-custom">
        <div className="max-w-4xl mx-auto">
          {/* Main Headline */}
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
            <span className="block">MissionMe</span>
            <span className="block text-2xl md:text-3xl lg:text-4xl font-normal mt-2 opacity-90">
              With You, Every Stroke of the Way
            </span>
          </h1>

          {/* Subtitle */}
          <p className="text-xl md:text-2xl mb-8 opacity-90 max-w-3xl mx-auto leading-relaxed">
            Life coaching by Nisso Barokas. Inspiring, guiding, and coaching
            individuals through life transitions, personal development, and
            emotional intelligence.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <Link
              href="#contact"
              className="bg-white text-blue-600 hover:bg-gray-100 font-semibold py-4 px-8 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 text-lg"
            >
              Start Your Journey
            </Link>
            <Link
              href="#about-missionme"
              className="border-2 border-white text-white hover:bg-white hover:text-blue-600 font-semibold py-4 px-8 rounded-lg transition-all duration-300 text-lg flex items-center gap-2"
            >
              <Play size={20} />
              Learn More
            </Link>
          </div>

          {/* Nisso's Photo Placeholder */}
          <div className="mb-8">
            <div className="w-32 h-32 mx-auto rounded-full bg-white/20 backdrop-blur-sm border-4 border-white/30 flex items-center justify-center">
              <span className="text-2xl font-bold">NB</span>
            </div>
            <p className="mt-4 text-lg opacity-90">Nisso Barokas</p>
            <p className="text-sm opacity-75">
              Life Coach & Transformation Guide
            </p>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <ChevronDown size={32} className="text-white opacity-70" />
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-white to-transparent z-5"></div>
    </section>
  );
};

export default Hero;
