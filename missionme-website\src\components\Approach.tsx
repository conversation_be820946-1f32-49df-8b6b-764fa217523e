import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Spark<PERSON> } from 'lucide-react';

const Approach = () => {
  const steps = [
    {
      number: "01",
      icon: <Eye className="w-8 h-8 text-white" />,
      title: "Clarity",
      subtitle: "See What Matters",
      description: "We start by cutting through the noise and confusion to help you see your situation clearly. What are the real issues? What do you truly want? What's holding you back?",
      color: "from-blue-500 to-blue-600"
    },
    {
      number: "02",
      icon: <Zap className="w-8 h-8 text-white" />,
      title: "Confidence",
      subtitle: "Build Inner Strength",
      description: "With clarity comes confidence. We help you recognize your strengths, address limiting beliefs, and develop the emotional intelligence to navigate challenges.",
      color: "from-blue-600 to-blue-700"
    },
    {
      number: "03",
      icon: <ArrowRight className="w-8 h-8 text-white" />,
      title: "Action",
      subtitle: "Take Meaningful Steps",
      description: "Insight without action is just therapy. We create concrete, achievable steps that move you forward, with accountability and support along the way.",
      color: "from-blue-700 to-blue-800"
    },
    {
      number: "04",
      icon: <Sparkles className="w-8 h-8 text-white" />,
      title: "Transformation",
      subtitle: "Become Who You're Meant to Be",
      description: "Real change happens when new patterns become natural. We help you integrate your growth into lasting transformation that serves you for life.",
      color: "from-blue-800 to-blue-900"
    }
  ];

  return (
    <section id="approach" className="section-padding bg-gray-50">
      <div className="container-custom">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Nisso's Approach
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            A proven four-part framework that transforms confusion into clarity, 
            doubt into confidence, and dreams into reality.
          </p>
        </div>

        {/* Desktop Layout */}
        <div className="hidden lg:block">
          <div className="relative">
            {/* Connection Line */}
            <div className="absolute top-1/2 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 to-blue-900 transform -translate-y-1/2 z-0"></div>
            
            <div className="grid grid-cols-4 gap-8 relative z-10">
              {steps.map((step, index) => (
                <div key={index} className="text-center">
                  {/* Icon Circle */}
                  <div className={`w-20 h-20 mx-auto mb-6 rounded-full bg-gradient-to-br ${step.color} flex items-center justify-center shadow-lg`}>
                    {step.icon}
                  </div>
                  
                  {/* Content */}
                  <div className="bg-white rounded-xl p-6 shadow-lg card-hover">
                    <div className="text-sm font-bold text-blue-600 mb-2">{step.number}</div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">{step.title}</h3>
                    <h4 className="text-lg font-medium text-blue-600 mb-4">{step.subtitle}</h4>
                    <p className="text-gray-600 leading-relaxed">{step.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Mobile Layout */}
        <div className="lg:hidden space-y-8">
          {steps.map((step, index) => (
            <div key={index} className="flex gap-6">
              {/* Icon and Line */}
              <div className="flex flex-col items-center">
                <div className={`w-16 h-16 rounded-full bg-gradient-to-br ${step.color} flex items-center justify-center shadow-lg flex-shrink-0`}>
                  {step.icon}
                </div>
                {index < steps.length - 1 && (
                  <div className="w-1 h-16 bg-gradient-to-b from-blue-500 to-blue-700 mt-4"></div>
                )}
              </div>
              
              {/* Content */}
              <div className="bg-white rounded-xl p-6 shadow-lg card-hover flex-1">
                <div className="text-sm font-bold text-blue-600 mb-2">{step.number}</div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">{step.title}</h3>
                <h4 className="text-lg font-medium text-blue-600 mb-4">{step.subtitle}</h4>
                <p className="text-gray-600 leading-relaxed">{step.description}</p>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="bg-white rounded-2xl shadow-xl p-8 md:p-12 max-w-4xl mx-auto">
            <h3 className="text-3xl font-bold text-gray-900 mb-6">
              Ready to Start Your Transformation?
            </h3>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              Every journey begins with a single conversation. Let's explore how this framework 
              can work for your unique situation and goals.
            </p>
            <a
              href="#contact"
              className="btn-primary text-lg"
            >
              Schedule Your Discovery Call
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Approach;
