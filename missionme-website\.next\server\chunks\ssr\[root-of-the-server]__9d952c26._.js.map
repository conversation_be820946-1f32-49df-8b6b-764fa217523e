{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Mission%20me/missionme-website/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { Menu, X } from 'lucide-react';\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navigation = [\n    { name: 'Home', href: '#home' },\n    { name: 'About MissionMe', href: '#about-missionme' },\n    { name: 'About Nisso', href: '#about-nisso' },\n    { name: 'Approach', href: '#approach' },\n    { name: 'Services', href: '#services' },\n    { name: 'Testimonials', href: '#testimonials' },\n    { name: 'Blog', href: '#blog' },\n    { name: 'Contact', href: '#contact' },\n  ];\n\n  return (\n    <header\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n        isScrolled\n          ? 'bg-white/95 backdrop-blur-md shadow-lg'\n          : 'bg-transparent'\n      }`}\n    >\n      <nav className=\"container-custom\">\n        <div className=\"flex items-center justify-between h-16 md:h-20\">\n          {/* Logo */}\n          <Link href=\"#home\" className=\"flex items-center space-x-2\">\n            <div className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-700 rounded-full flex items-center justify-center\">\n              <span className=\"text-white font-bold text-lg\">M</span>\n            </div>\n            <span className=\"text-xl font-bold text-gray-900\">MissionMe</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden lg:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-gray-700 hover:text-blue-600 transition-colors duration-200 font-medium\"\n              >\n                {item.name}\n              </Link>\n            ))}\n            <Link\n              href=\"#contact\"\n              className=\"btn-primary\"\n            >\n              Let's Talk\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n            className=\"lg:hidden p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 transition-colors duration-200\"\n          >\n            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"lg:hidden absolute top-full left-0 right-0 bg-white shadow-lg border-t\">\n            <div className=\"py-4 space-y-2\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"block px-4 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors duration-200\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {item.name}\n                </Link>\n              ))}\n              <div className=\"px-4 pt-2\">\n                <Link\n                  href=\"#contact\"\n                  className=\"btn-primary w-full text-center\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  Let's Talk\n                </Link>\n              </div>\n            </div>\n          </div>\n        )}\n      </nav>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAMA,MAAM,SAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAmB,MAAM;QAAmB;QACpD;YAAE,MAAM;YAAe,MAAM;QAAe;QAC5C;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAgB,MAAM;QAAgB;QAC9C;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,qBACE,8OAAC;QACC,WAAW,CAAC,4DAA4D,EACtE,aACI,2CACA,kBACJ;kBAEF,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAQ,WAAU;;8CAC3B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;sCAIpD,8OAAC;4BAAI,WAAU;;gCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,IAAI;uCAJL,KAAK,IAAI;;;;;8CAOlB,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC;4BACC,SAAS,IAAM,cAAc,CAAC;4BAC9B,WAAU;sCAET,2BAAa,8OAAC,4LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;qDAAS,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;;;;;;;;;;;;gBAK/C,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,cAAc;8CAE5B,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;0CAQlB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,cAAc;8CAC9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;uCAEe", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Mission%20me/missionme-website/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { ChevronDown, Play } from 'lucide-react';\nimport Link from 'next/link';\n\nconst Hero = () => {\n  const [isVideoLoaded, setIsVideoLoaded] = useState(false);\n\n  useEffect(() => {\n    // Simulate video loading\n    const timer = setTimeout(() => setIsVideoLoaded(true), 1000);\n    return () => clearTimeout(timer);\n  }, []);\n\n  return (\n    <section id=\"home\" className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background Video Placeholder */}\n      <div className=\"absolute inset-0 z-0\">\n        {isVideoLoaded ? (\n          <div className=\"w-full h-full bg-gradient-to-br from-blue-400 via-blue-600 to-blue-800 opacity-90\">\n            {/* Animated water effect */}\n            <div className=\"absolute inset-0 bg-wave opacity-70\"></div>\n            <div className=\"absolute inset-0\">\n              <div className=\"absolute top-1/4 left-1/4 w-32 h-32 bg-blue-300 rounded-full opacity-20 animate-pulse\"></div>\n              <div className=\"absolute top-3/4 right-1/4 w-24 h-24 bg-blue-200 rounded-full opacity-30 animate-pulse delay-1000\"></div>\n              <div className=\"absolute bottom-1/4 left-1/3 w-20 h-20 bg-blue-400 rounded-full opacity-25 animate-pulse delay-500\"></div>\n            </div>\n          </div>\n        ) : (\n          <div className=\"w-full h-full bg-gradient-to-br from-blue-400 to-blue-800 flex items-center justify-center\">\n            <div className=\"text-white text-lg\">Loading...</div>\n          </div>\n        )}\n      </div>\n\n      {/* Content Overlay */}\n      <div className=\"relative z-10 text-center text-white container-custom\">\n        <div className=\"max-w-4xl mx-auto\">\n          {/* Main Headline */}\n          <h1 className=\"text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight\">\n            <span className=\"block\">MissionMe</span>\n            <span className=\"block text-2xl md:text-3xl lg:text-4xl font-normal mt-2 opacity-90\">\n              With You, Every Stroke of the Way\n            </span>\n          </h1>\n\n          {/* Subtitle */}\n          <p className=\"text-xl md:text-2xl mb-8 opacity-90 max-w-3xl mx-auto leading-relaxed\">\n            Life coaching by Nisso Barokas. Inspiring, guiding, and coaching individuals \n            through life transitions, personal development, and emotional intelligence.\n          </p>\n\n          {/* CTA Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\">\n            <Link\n              href=\"#contact\"\n              className=\"bg-white text-blue-600 hover:bg-gray-100 font-semibold py-4 px-8 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 text-lg\"\n            >\n              Start Your Journey\n            </Link>\n            <Link\n              href=\"#about-missionme\"\n              className=\"border-2 border-white text-white hover:bg-white hover:text-blue-600 font-semibold py-4 px-8 rounded-lg transition-all duration-300 text-lg flex items-center gap-2\"\n            >\n              <Play size={20} />\n              Learn More\n            </Link>\n          </div>\n\n          {/* Nisso's Photo Placeholder */}\n          <div className=\"mb-8\">\n            <div className=\"w-32 h-32 mx-auto rounded-full bg-white/20 backdrop-blur-sm border-4 border-white/30 flex items-center justify-center\">\n              <span className=\"text-2xl font-bold\">NB</span>\n            </div>\n            <p className=\"mt-4 text-lg opacity-90\">Nisso Barokas</p>\n            <p className=\"text-sm opacity-75\">Life Coach & Transformation Guide</p>\n          </div>\n        </div>\n\n        {/* Scroll Indicator */}\n        <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n          <ChevronDown size={32} className=\"text-white opacity-70\" />\n        </div>\n      </div>\n\n      {/* Decorative Elements */}\n      <div className=\"absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-white to-transparent z-5\"></div>\n    </section>\n  );\n};\n\nexport default Hero;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAMA,MAAM,OAAO;IACX,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yBAAyB;QACzB,MAAM,QAAQ,WAAW,IAAM,iBAAiB,OAAO;QACvD,OAAO,IAAM,aAAa;IAC5B,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAQ,IAAG;QAAO,WAAU;;0BAE3B,8OAAC;gBAAI,WAAU;0BACZ,8BACC,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;yCAInB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCAAqB;;;;;;;;;;;;;;;;0BAM1C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAAQ;;;;;;kDACxB,8OAAC;wCAAK,WAAU;kDAAqE;;;;;;;;;;;;0CAMvF,8OAAC;gCAAE,WAAU;0CAAwE;;;;;;0CAMrF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,MAAM;;;;;;4CAAM;;;;;;;;;;;;;0CAMtB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;kDAEvC,8OAAC;wCAAE,WAAU;kDAA0B;;;;;;kDACvC,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;;;;;;;kCAKtC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,oNAAA,CAAA,cAAW;4BAAC,MAAM;4BAAI,WAAU;;;;;;;;;;;;;;;;;0BAKrC,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;uCAEe", "debugId": null}}, {"offset": {"line": 507, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Mission%20me/missionme-website/src/components/Testimonials.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { ChevronLeft, ChevronRight, Star } from 'lucide-react';\n\nconst Testimonials = () => {\n  const testimonials = [\n    {\n      name: \"<PERSON>\",\n      role: \"Marketing Executive\",\n      content: \"Working with <PERSON><PERSON> completely transformed how I approach challenges at work and in my personal life. His blend of practical wisdom and genuine humor made even the toughest conversations feel manageable. I finally have the confidence to pursue the career change I've been dreaming about.\",\n      rating: 5,\n      highlight: \"Transformed my approach to challenges\"\n    },\n    {\n      name: \"<PERSON>\",\n      role: \"Married Couple\",\n      content: \"After 15 years of marriage, we thought we knew how to communicate. <PERSON><PERSON> showed us we were just scratching the surface. The tools he gave us for emotional intelligence and conflict resolution have brought us closer than we've ever been. Our kids notice the difference too.\",\n      rating: 5,\n      highlight: \"Brought us closer than ever\"\n    },\n    {\n      name: \"<PERSON>\",\n      role: \"Recent Graduate\",\n      content: \"As a 25-year-old feeling completely lost after college, <PERSON><PERSON> helped me find direction and purpose. He didn't just tell me what to do—he helped me discover what I actually wanted. Six months later, I'm in a job I love and have the emotional tools to handle whatever comes next.\",\n      rating: 5,\n      highlight: \"Helped me find direction and purpose\"\n    },\n    {\n      name: \"<PERSON>\",\n      role: \"Small Business Owner\",\n      content: \"<PERSON><PERSON>'s background in business and leadership was exactly what I needed. He understood the pressures I face as an entrepreneur while helping me develop the emotional intelligence to lead my team better. My business is thriving, and more importantly, I'm enjoying the journey.\",\n      rating: 5,\n      highlight: \"Business thriving and enjoying the journey\"\n    },\n    {\n      name: \"The <PERSON> Family\",\n      role: \"Family of Four\",\n      content: \"Our teenage kids were struggling, and family dinners had become battlefields. Nisso helped us rebuild communication and understanding. He taught us that humor and empathy could coexist with boundaries and expectations. Our home feels peaceful again.\",\n      rating: 5,\n      highlight: \"Our home feels peaceful again\"\n    }\n  ];\n\n  const [currentIndex, setCurrentIndex] = useState(0);\n\n  const nextTestimonial = () => {\n    setCurrentIndex((prev) => (prev + 1) % testimonials.length);\n  };\n\n  const prevTestimonial = () => {\n    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);\n  };\n\n  return (\n    <section id=\"testimonials\" className=\"section-padding bg-gray-50\">\n      <div className=\"container-custom\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            Client Success Stories\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            Real transformations from real people. These are the stories that fuel our passion \n            for helping others discover their potential and create meaningful change.\n          </p>\n        </div>\n\n        {/* Carousel */}\n        <div className=\"relative max-w-4xl mx-auto\">\n          <div className=\"bg-white rounded-2xl shadow-xl p-8 md:p-12\">\n            {/* Stars */}\n            <div className=\"flex justify-center mb-6\">\n              {[...Array(testimonials[currentIndex].rating)].map((_, i) => (\n                <Star key={i} className=\"w-6 h-6 text-yellow-400 fill-current\" />\n              ))}\n            </div>\n\n            {/* Testimonial Content */}\n            <blockquote className=\"text-xl md:text-2xl text-gray-700 italic text-center mb-8 leading-relaxed\">\n              \"{testimonials[currentIndex].content}\"\n            </blockquote>\n\n            {/* Highlight */}\n            <div className=\"text-center mb-6\">\n              <span className=\"inline-block bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium\">\n                {testimonials[currentIndex].highlight}\n              </span>\n            </div>\n\n            {/* Author */}\n            <div className=\"text-center\">\n              <cite className=\"text-lg font-semibold text-gray-900 not-italic\">\n                {testimonials[currentIndex].name}\n              </cite>\n              <p className=\"text-blue-600 font-medium\">\n                {testimonials[currentIndex].role}\n              </p>\n            </div>\n          </div>\n\n          {/* Navigation Buttons */}\n          <button\n            onClick={prevTestimonial}\n            className=\"absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center text-gray-600 hover:text-blue-600 hover:shadow-xl transition-all duration-300\"\n          >\n            <ChevronLeft size={24} />\n          </button>\n          <button\n            onClick={nextTestimonial}\n            className=\"absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center text-gray-600 hover:text-blue-600 hover:shadow-xl transition-all duration-300\"\n          >\n            <ChevronRight size={24} />\n          </button>\n\n          {/* Dots Indicator */}\n          <div className=\"flex justify-center mt-8 space-x-2\">\n            {testimonials.map((_, index) => (\n              <button\n                key={index}\n                onClick={() => setCurrentIndex(index)}\n                className={`w-3 h-3 rounded-full transition-all duration-300 ${\n                  index === currentIndex\n                    ? 'bg-blue-600 w-8'\n                    : 'bg-gray-300 hover:bg-gray-400'\n                }`}\n              />\n            ))}\n          </div>\n        </div>\n\n        {/* Stats */}\n        <div className=\"grid md:grid-cols-3 gap-8 mt-16\">\n          <div className=\"text-center\">\n            <div className=\"text-4xl font-bold text-blue-600 mb-2\">95%</div>\n            <p className=\"text-gray-600\">Client Satisfaction Rate</p>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-4xl font-bold text-blue-600 mb-2\">200+</div>\n            <p className=\"text-gray-600\">Lives Transformed</p>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-4xl font-bold text-blue-600 mb-2\">10+</div>\n            <p className=\"text-gray-600\">Years of Experience</p>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Testimonials;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,eAAe;IACnB,MAAM,eAAe;QACnB;YACE,MAAM;YACN,MAAM;YACN,SAAS;YACT,QAAQ;YACR,WAAW;QACb;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS;YACT,QAAQ;YACR,WAAW;QACb;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS;YACT,QAAQ;YACR,WAAW;QACb;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS;YACT,QAAQ;YACR,WAAW;QACb;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS;YACT,QAAQ;YACR,WAAW;QACb;KACD;IAED,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,kBAAkB;QACtB,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,aAAa,MAAM;IAC5D;IAEA,MAAM,kBAAkB;QACtB,gBAAgB,CAAC,OAAS,CAAC,OAAO,IAAI,aAAa,MAAM,IAAI,aAAa,MAAM;IAClF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAe,WAAU;kBACnC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAOzE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ;2CAAI,MAAM,YAAY,CAAC,aAAa,CAAC,MAAM;qCAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrD,8OAAC,kMAAA,CAAA,OAAI;4CAAS,WAAU;2CAAb;;;;;;;;;;8CAKf,8OAAC;oCAAW,WAAU;;wCAA4E;wCAC9F,YAAY,CAAC,aAAa,CAAC,OAAO;wCAAC;;;;;;;8CAIvC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDACb,YAAY,CAAC,aAAa,CAAC,SAAS;;;;;;;;;;;8CAKzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDACb,YAAY,CAAC,aAAa,CAAC,IAAI;;;;;;sDAElC,8OAAC;4CAAE,WAAU;sDACV,YAAY,CAAC,aAAa,CAAC,IAAI;;;;;;;;;;;;;;;;;;sCAMtC,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;gCAAC,MAAM;;;;;;;;;;;sCAErB,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;gCAAC,MAAM;;;;;;;;;;;sCAItB,8OAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,GAAG,sBACpB,8OAAC;oCAEC,SAAS,IAAM,gBAAgB;oCAC/B,WAAW,CAAC,iDAAiD,EAC3D,UAAU,eACN,oBACA,iCACJ;mCANG;;;;;;;;;;;;;;;;8BAab,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAwC;;;;;;8CACvD,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAE/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAwC;;;;;;8CACvD,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAE/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAwC;;;;;;8CACvD,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC;uCAEe", "debugId": null}}, {"offset": {"line": 829, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Mission%20me/missionme-website/src/components/FAQ.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { ChevronDown, ChevronUp } from 'lucide-react';\n\nconst FAQ = () => {\n  const [openIndex, setOpenIndex] = useState<number | null>(0);\n\n  const faqs = [\n    {\n      question: \"What's the difference between therapy and life coaching?\",\n      answer: \"While therapy often focuses on healing past wounds and addressing mental health conditions, life coaching is forward-focused on achieving goals and creating positive change. Therapy asks 'Why?' while coaching asks 'What now?' and 'How?' As a life coach, I help you identify what you want, create actionable plans, and develop the skills to get there. If you're dealing with serious mental health issues, I'll recommend working with a licensed therapist alongside or instead of coaching.\"\n    },\n    {\n      question: \"How long does coaching typically take?\",\n      answer: \"Every person and situation is unique, but most clients see significant progress within 3-6 months of regular sessions. Some people achieve their goals in a few months, while others prefer ongoing support for continued growth. During our discovery call, we'll discuss your specific goals and create a timeline that makes sense for your situation. The beauty of coaching is that you're in control of the pace and duration.\"\n    },\n    {\n      question: \"Do you offer online sessions?\",\n      answer: \"Absolutely! I offer both in-person and online sessions via secure video conferencing. Many of my clients actually prefer online sessions for the convenience and comfort of being in their own space. The coaching experience is just as effective virtually, and it allows me to work with clients regardless of their location. We'll determine what works best for you during our initial conversation.\"\n    },\n    {\n      question: \"What if I'm not sure coaching is right for me?\",\n      answer: \"That's exactly why I offer a free 30-minute discovery call! This gives us both a chance to explore your situation, discuss your goals, and see if we're a good fit. There's no pressure or obligation. Coaching works best when there's a strong connection and mutual trust, so it's important that you feel comfortable and confident in the process. If coaching isn't the right fit, I'm happy to suggest other resources that might better serve your needs.\"\n    },\n    {\n      question: \"How much does coaching cost?\",\n      answer: \"Coaching packages are customized based on your specific needs, goals, and the level of support you're looking for. During our discovery call, we'll discuss your situation and I'll recommend the best approach and investment for your circumstances. I believe in making coaching accessible and will work with you to find a solution that fits your budget while providing the support you need to succeed.\"\n    },\n    {\n      question: \"What makes your approach different from other coaches?\",\n      answer: \"My unique background combines military leadership, business experience, and deep emotional intelligence training. I bring real-world experience from leading teams under pressure, building successful businesses, and navigating my own life transitions. But what really sets me apart is my belief that humor and humanity are essential ingredients in transformation. I don't just give advice—I walk alongside you with empathy, practical tools, and yes, the occasional well-timed joke.\"\n    },\n    {\n      question: \"Do you work with couples and families?\",\n      answer: \"Yes! I work with individuals, couples, and families. Relationship coaching focuses on improving communication, resolving conflicts, and building stronger emotional connections. Family coaching helps navigate dynamics between parents and children, siblings, or extended family members. Each situation is unique, and I tailor my approach to address the specific challenges and goals of your relationship or family system.\"\n    },\n    {\n      question: \"What happens between sessions?\",\n      answer: \"Coaching doesn't stop when our session ends! You'll typically receive exercises, tools, or 'homework' to practice between sessions. I'm also available via email for quick questions or support. The real transformation happens in your daily life as you apply what we discuss. I provide resources, check-ins, and accountability to help you stay on track and make consistent progress toward your goals.\"\n    }\n  ];\n\n  const toggleFAQ = (index: number) => {\n    setOpenIndex(openIndex === index ? null : index);\n  };\n\n  return (\n    <section id=\"faq\" className=\"section-padding bg-white\">\n      <div className=\"container-custom\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            Frequently Asked Questions\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            Get answers to common questions about life coaching, the process, \n            and what you can expect when working with MissionMe.\n          </p>\n        </div>\n\n        <div className=\"max-w-4xl mx-auto\">\n          <div className=\"space-y-4\">\n            {faqs.map((faq, index) => (\n              <div\n                key={index}\n                className=\"bg-gray-50 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300\"\n              >\n                <button\n                  onClick={() => toggleFAQ(index)}\n                  className=\"w-full px-6 py-6 text-left flex items-center justify-between hover:bg-gray-100 transition-colors duration-200\"\n                >\n                  <h3 className=\"text-lg font-semibold text-gray-900 pr-4\">\n                    {faq.question}\n                  </h3>\n                  <div className=\"flex-shrink-0\">\n                    {openIndex === index ? (\n                      <ChevronUp className=\"w-5 h-5 text-blue-600\" />\n                    ) : (\n                      <ChevronDown className=\"w-5 h-5 text-gray-400\" />\n                    )}\n                  </div>\n                </button>\n                \n                {openIndex === index && (\n                  <div className=\"px-6 pb-6\">\n                    <div className=\"pt-2 border-t border-gray-200\">\n                      <p className=\"text-gray-700 leading-relaxed\">\n                        {faq.answer}\n                      </p>\n                    </div>\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Still Have Questions CTA */}\n        <div className=\"text-center mt-16\">\n          <div className=\"bg-blue-50 rounded-2xl p-8 md:p-12 max-w-3xl mx-auto\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              Still Have Questions?\n            </h3>\n            <p className=\"text-gray-700 mb-6 leading-relaxed\">\n              Every situation is unique, and I'm here to address any specific concerns \n              or questions you might have about coaching and how it can help you.\n            </p>\n            <a\n              href=\"#contact\"\n              className=\"btn-primary\"\n            >\n              Schedule a Free Discovery Call\n            </a>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default FAQ;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAKA,MAAM,MAAM;IACV,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1D,MAAM,OAAO;QACX;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;KACD;IAED,MAAM,YAAY,CAAC;QACjB,aAAa,cAAc,QAAQ,OAAO;IAC5C;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAM,WAAU;kBAC1B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAMzE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAU;;0DAEV,8OAAC;gDAAG,WAAU;0DACX,IAAI,QAAQ;;;;;;0DAEf,8OAAC;gDAAI,WAAU;0DACZ,cAAc,sBACb,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;yEAErB,8OAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;;;;;;oCAK5B,cAAc,uBACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DACV,IAAI,MAAM;;;;;;;;;;;;;;;;;+BAvBd;;;;;;;;;;;;;;;8BAkCb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;0CAIlD,8OAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}, {"offset": {"line": 1053, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Mission%20me/missionme-website/src/components/PersonalQuotes.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Quote } from 'lucide-react';\n\nconst PersonalQuotes = () => {\n  const quotes = [\n    {\n      text: \"Every failure is a gift—you just have to open the box.\",\n      context: \"On reframing setbacks as learning opportunities\"\n    },\n    {\n      text: \"Curiosity is my superpower.\",\n      context: \"On the power of asking questions instead of making assumptions\"\n    },\n    {\n      text: \"If you're reading this, you're already in the top 1%.\",\n      context: \"On recognizing the privilege of seeking growth and self-improvement\"\n    },\n    {\n      text: \"Emotional intelligence isn't a nice-to-have—it's your competitive advantage.\",\n      context: \"On the importance of developing emotional skills\"\n    },\n    {\n      text: \"The best leaders are the ones who make other people feel like leaders.\",\n      context: \"On authentic leadership and empowerment\"\n    },\n    {\n      text: \"Change happens in the space between comfort and panic.\",\n      context: \"On finding the optimal zone for growth and transformation\"\n    },\n    {\n      text: \"Your story isn't over—you're just turning the page.\",\n      context: \"On resilience and the possibility of new beginnings\"\n    },\n    {\n      text: \"Humor isn't the opposite of seriousness—it's the antidote to despair.\",\n      context: \"On the healing power of laughter and lightness\"\n    }\n  ];\n\n  const [currentQuote, setCurrentQuote] = useState(0);\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentQuote((prev) => (prev + 1) % quotes.length);\n    }, 5000);\n\n    return () => clearInterval(interval);\n  }, [quotes.length]);\n\n  return (\n    <section className=\"section-padding bg-gradient-to-br from-blue-600 to-blue-800 text-white\">\n      <div className=\"container-custom\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold mb-6\">\n            Words to Live By\n          </h2>\n          <p className=\"text-xl opacity-90 max-w-3xl mx-auto leading-relaxed\">\n            Insights and wisdom from years of coaching, leading, and learning. \n            These are the principles that guide our work together.\n          </p>\n        </div>\n\n        {/* Featured Quote */}\n        <div className=\"max-w-4xl mx-auto text-center mb-16\">\n          <div className=\"relative\">\n            <Quote className=\"w-16 h-16 text-blue-300 mx-auto mb-6 opacity-50\" />\n            <blockquote className=\"text-3xl md:text-4xl lg:text-5xl font-bold mb-6 leading-tight\">\n              \"{quotes[currentQuote].text}\"\n            </blockquote>\n            <p className=\"text-lg md:text-xl opacity-80 italic\">\n              {quotes[currentQuote].context}\n            </p>\n          </div>\n        </div>\n\n        {/* Quote Navigation Dots */}\n        <div className=\"flex justify-center space-x-2 mb-16\">\n          {quotes.map((_, index) => (\n            <button\n              key={index}\n              onClick={() => setCurrentQuote(index)}\n              className={`w-3 h-3 rounded-full transition-all duration-300 ${\n                index === currentQuote\n                  ? 'bg-white w-8'\n                  : 'bg-blue-300 hover:bg-blue-200'\n              }`}\n            />\n          ))}\n        </div>\n\n        {/* All Quotes Grid */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {quotes.map((quote, index) => (\n            <div\n              key={index}\n              className={`bg-white/10 backdrop-blur-sm rounded-xl p-6 transition-all duration-300 hover:bg-white/20 cursor-pointer ${\n                index === currentQuote ? 'ring-2 ring-white/50' : ''\n              }`}\n              onClick={() => setCurrentQuote(index)}\n            >\n              <blockquote className=\"text-lg font-medium mb-3\">\n                \"{quote.text}\"\n              </blockquote>\n              <p className=\"text-sm opacity-75 italic\">\n                {quote.context}\n              </p>\n            </div>\n          ))}\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"text-center mt-16\">\n          <div className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-8 md:p-12 max-w-3xl mx-auto\">\n            <h3 className=\"text-2xl font-bold mb-4\">\n              Ready to Write Your Next Chapter?\n            </h3>\n            <p className=\"text-lg opacity-90 mb-6 leading-relaxed\">\n              These principles come alive in our coaching conversations. Let's explore \n              how they can guide your own journey of growth and transformation.\n            </p>\n            <a\n              href=\"#contact\"\n              className=\"bg-white text-blue-600 hover:bg-gray-100 font-semibold py-3 px-8 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 inline-block\"\n            >\n              Start the Conversation\n            </a>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default PersonalQuotes;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,iBAAiB;IACrB,MAAM,SAAS;QACb;YACE,MAAM;YACN,SAAS;QACX;QACA;YACE,MAAM;YACN,SAAS;QACX;QACA;YACE,MAAM;YACN,SAAS;QACX;QACA;YACE,MAAM;YACN,SAAS;QACX;QACA;YACE,MAAM;YACN,SAAS;QACX;QACA;YACE,MAAM;YACN,SAAS;QACX;QACA;YACE,MAAM;YACN,SAAS;QACX;QACA;YACE,MAAM;YACN,SAAS;QACX;KACD;IAED,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,OAAO,MAAM;QACtD,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC,OAAO,MAAM;KAAC;IAElB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,8OAAC;4BAAE,WAAU;sCAAuD;;;;;;;;;;;;8BAOtE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAW,WAAU;;oCAAgE;oCAClF,MAAM,CAAC,aAAa,CAAC,IAAI;oCAAC;;;;;;;0CAE9B,8OAAC;gCAAE,WAAU;0CACV,MAAM,CAAC,aAAa,CAAC,OAAO;;;;;;;;;;;;;;;;;8BAMnC,8OAAC;oBAAI,WAAU;8BACZ,OAAO,GAAG,CAAC,CAAC,GAAG,sBACd,8OAAC;4BAEC,SAAS,IAAM,gBAAgB;4BAC/B,WAAW,CAAC,iDAAiD,EAC3D,UAAU,eACN,iBACA,iCACJ;2BANG;;;;;;;;;;8BAYX,8OAAC;oBAAI,WAAU;8BACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;4BAEC,WAAW,CAAC,yGAAyG,EACnH,UAAU,eAAe,yBAAyB,IAClD;4BACF,SAAS,IAAM,gBAAgB;;8CAE/B,8OAAC;oCAAW,WAAU;;wCAA2B;wCAC7C,MAAM,IAAI;wCAAC;;;;;;;8CAEf,8OAAC;oCAAE,WAAU;8CACV,MAAM,OAAO;;;;;;;2BAVX;;;;;;;;;;8BAiBX,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CAGxC,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;0CAIvD,8OAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}, {"offset": {"line": 1292, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Mission%20me/missionme-website/src/components/Contact.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Mail, Phone, MapPin, Send, CheckCircle } from 'lucide-react';\n\nconst Contact = () => {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    service: '',\n    message: ''\n  });\n  const [isSubmitted, setIsSubmitted] = useState(false);\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // Here you would typically send the form data to your backend\n    console.log('Form submitted:', formData);\n    setIsSubmitted(true);\n    \n    // Reset form after 3 seconds\n    setTimeout(() => {\n      setIsSubmitted(false);\n      setFormData({\n        name: '',\n        email: '',\n        phone: '',\n        service: '',\n        message: ''\n      });\n    }, 3000);\n  };\n\n  const contactInfo = [\n    {\n      icon: <Mail className=\"w-6 h-6 text-blue-600\" />,\n      title: \"Email\",\n      content: \"<EMAIL>\",\n      link: \"mailto:<EMAIL>\"\n    },\n    {\n      icon: <Phone className=\"w-6 h-6 text-blue-600\" />,\n      title: \"Phone\",\n      content: \"+1 (*************\",\n      link: \"tel:+15551234567\"\n    },\n    {\n      icon: <MapPin className=\"w-6 h-6 text-blue-600\" />,\n      title: \"Location\",\n      content: \"Available Online & In-Person\",\n      link: null\n    }\n  ];\n\n  return (\n    <section id=\"contact\" className=\"section-padding bg-white\">\n      <div className=\"container-custom\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            Let's Start the Conversation\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            Ready to take the next step in your journey? Reach out for a free discovery call \n            and let's explore how coaching can help you achieve your goals.\n          </p>\n        </div>\n\n        <div className=\"grid lg:grid-cols-2 gap-16\">\n          {/* Contact Form */}\n          <div className=\"bg-gray-50 rounded-2xl p-8\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-6\">\n              Send a Message\n            </h3>\n            \n            {isSubmitted ? (\n              <div className=\"text-center py-12\">\n                <CheckCircle className=\"w-16 h-16 text-green-500 mx-auto mb-4\" />\n                <h4 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                  Thank You!\n                </h4>\n                <p className=\"text-gray-600\">\n                  Your message has been sent. I'll get back to you within 24 hours.\n                </p>\n              </div>\n            ) : (\n              <form onSubmit={handleSubmit} className=\"space-y-6\">\n                <div className=\"grid md:grid-cols-2 gap-6\">\n                  <div>\n                    <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Full Name *\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"name\"\n                      name=\"name\"\n                      required\n                      value={formData.name}\n                      onChange={handleChange}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent\"\n                      placeholder=\"Your full name\"\n                    />\n                  </div>\n                  <div>\n                    <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Email Address *\n                    </label>\n                    <input\n                      type=\"email\"\n                      id=\"email\"\n                      name=\"email\"\n                      required\n                      value={formData.email}\n                      onChange={handleChange}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent\"\n                      placeholder=\"<EMAIL>\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"grid md:grid-cols-2 gap-6\">\n                  <div>\n                    <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Phone Number\n                    </label>\n                    <input\n                      type=\"tel\"\n                      id=\"phone\"\n                      name=\"phone\"\n                      value={formData.phone}\n                      onChange={handleChange}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent\"\n                      placeholder=\"(*************\"\n                    />\n                  </div>\n                  <div>\n                    <label htmlFor=\"service\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Service Interest\n                    </label>\n                    <select\n                      id=\"service\"\n                      name=\"service\"\n                      value={formData.service}\n                      onChange={handleChange}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent\"\n                    >\n                      <option value=\"\">Select a service</option>\n                      <option value=\"individual\">Individual Coaching</option>\n                      <option value=\"couples\">Couples Coaching</option>\n                      <option value=\"family\">Family Coaching</option>\n                      <option value=\"young-adult\">Young Adult Coaching</option>\n                      <option value=\"group\">Group Workshops</option>\n                      <option value=\"not-sure\">Not Sure Yet</option>\n                    </select>\n                  </div>\n                </div>\n\n                <div>\n                  <label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Message *\n                  </label>\n                  <textarea\n                    id=\"message\"\n                    name=\"message\"\n                    required\n                    rows={5}\n                    value={formData.message}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent resize-none\"\n                    placeholder=\"Tell me a bit about your situation and what you're hoping to achieve...\"\n                  />\n                </div>\n\n                <button\n                  type=\"submit\"\n                  className=\"w-full btn-primary flex items-center justify-center gap-2\"\n                >\n                  <Send className=\"w-5 h-5\" />\n                  Send Message\n                </button>\n              </form>\n            )}\n          </div>\n\n          {/* Contact Information */}\n          <div>\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-6\">\n              Get in Touch\n            </h3>\n            \n            <div className=\"space-y-6 mb-8\">\n              {contactInfo.map((info, index) => (\n                <div key={index} className=\"flex items-start gap-4\">\n                  <div className=\"flex-shrink-0 w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center\">\n                    {info.icon}\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900 mb-1\">\n                      {info.title}\n                    </h4>\n                    {info.link ? (\n                      <a\n                        href={info.link}\n                        className=\"text-gray-600 hover:text-blue-600 transition-colors duration-200\"\n                      >\n                        {info.content}\n                      </a>\n                    ) : (\n                      <p className=\"text-gray-600\">{info.content}</p>\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* Discovery Call CTA */}\n            <div className=\"bg-blue-50 rounded-xl p-6\">\n              <h4 className=\"text-xl font-semibold text-gray-900 mb-3\">\n                Free Discovery Call\n              </h4>\n              <p className=\"text-gray-700 mb-4 leading-relaxed\">\n                Not sure if coaching is right for you? Let's have a 30-minute conversation \n                to explore your goals and see if we're a good fit. No pressure, no obligation.\n              </p>\n              <a\n                href=\"https://calendly.com/missionme\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"btn-secondary w-full text-center\"\n              >\n                Schedule Discovery Call\n              </a>\n            </div>\n\n            {/* Response Time */}\n            <div className=\"mt-8 p-4 bg-green-50 rounded-lg\">\n              <p className=\"text-green-800 text-sm\">\n                <strong>Response Time:</strong> I typically respond to messages within 24 hours. \n                For urgent matters, please call directly.\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Contact;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,UAAU;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,SAAS;IACX;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,8DAA8D;QAC9D,QAAQ,GAAG,CAAC,mBAAmB;QAC/B,eAAe;QAEf,6BAA6B;QAC7B,WAAW;YACT,eAAe;YACf,YAAY;gBACV,MAAM;gBACN,OAAO;gBACP,OAAO;gBACP,SAAS;gBACT,SAAS;YACX;QACF,GAAG;IACL;IAEA,MAAM,cAAc;QAClB;YACE,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,OAAO;YACP,SAAS;YACT,MAAM;QACR;QACA;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,SAAS;YACT,MAAM;QACR;QACA;YACE,oBAAM,8OAAC,0MAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,SAAS;YACT,MAAM;QACR;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAMzE,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;gCAIrD,4BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAGzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;yDAK/B,8OAAC;oCAAK,UAAU;oCAAc,WAAU;;sDACtC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAO,WAAU;sEAA+C;;;;;;sEAG/E,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,QAAQ;4DACR,OAAO,SAAS,IAAI;4DACpB,UAAU;4DACV,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAGhB,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAQ,WAAU;sEAA+C;;;;;;sEAGhF,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,QAAQ;4DACR,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;sDAKlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAQ,WAAU;sEAA+C;;;;;;sEAGhF,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAGhB,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAU,WAAU;sEAA+C;;;;;;sEAGlF,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,OAAO;4DACvB,UAAU;4DACV,WAAU;;8EAEV,8OAAC;oEAAO,OAAM;8EAAG;;;;;;8EACjB,8OAAC;oEAAO,OAAM;8EAAa;;;;;;8EAC3B,8OAAC;oEAAO,OAAM;8EAAU;;;;;;8EACxB,8OAAC;oEAAO,OAAM;8EAAS;;;;;;8EACvB,8OAAC;oEAAO,OAAM;8EAAc;;;;;;8EAC5B,8OAAC;oEAAO,OAAM;8EAAQ;;;;;;8EACtB,8OAAC;oEAAO,OAAM;8EAAW;;;;;;;;;;;;;;;;;;;;;;;;sDAK/B,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAU,WAAU;8DAA+C;;;;;;8DAGlF,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,QAAQ;oDACR,MAAM;oDACN,OAAO,SAAS,OAAO;oDACvB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;4CACC,MAAK;4CACL,WAAU;;8DAEV,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;;;;;;;sCAQpC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAItD,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;oDAAI,WAAU;8DACZ,KAAK,IAAI;;;;;;8DAEZ,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEACX,KAAK,KAAK;;;;;;wDAEZ,KAAK,IAAI,iBACR,8OAAC;4DACC,MAAM,KAAK,IAAI;4DACf,WAAU;sEAET,KAAK,OAAO;;;;;iFAGf,8OAAC;4DAAE,WAAU;sEAAiB,KAAK,OAAO;;;;;;;;;;;;;2CAhBtC;;;;;;;;;;8CAwBd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAGzD,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAIlD,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDACX;;;;;;;;;;;;8CAMH,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;0DACX,8OAAC;0DAAO;;;;;;4CAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/C;uCAEe", "debugId": null}}]}